[project]
name = "voice"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "chromadb>=1.0.15",
    "dotenv>=0.9.9",
    "httpx>=0.28.1",
    "langchain>=0.3.26",
    "langchain-chroma>=0.2.4",
    "langchain-core>=0.3.69",
    "langchain-google-genai>=2.1.8",
    "langchain-openai>=0.3.28",
    "langchain-text-splitters>=0.3.8",
    "mmh3>=5.1.0",
    "numpy>=2.2.6",
    "openai>=1.97.0",
    "overrides>=7.7.0",
    "pandas>=2.3.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "python-dotenv>=1.1.1",
    "pyyaml>=6.0.2",
    "requests>=2.32.4",
    "streamlit>=1.47.0",
    "tenacity>=9.1.2",
    "tiktoken>=0.9.0",
    "tokenizers>=0.21.2",
    "toml>=0.10.2",
]
