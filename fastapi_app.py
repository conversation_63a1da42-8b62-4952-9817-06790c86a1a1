"""
FastAPI Application for Twilio Integration with RAG Pipeline
Provides REST API endpoints for Twilio voice calls and maintains streaming functionality
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, Response, HTTPException, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.templating import Jin<PERSON>2Templates
import uvicorn
from dotenv import load_dotenv

# Import our existing RAG pipeline
from rag_pipeline import TelecomRAGPipeline

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global RAG pipeline instance
rag_pipeline: Optional[TelecomRAGPipeline] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global rag_pipeline
    
    # Startup
    logger.info("Starting FastAPI application...")
    try:
        rag_pipeline = TelecomRAGPipeline()
        logger.info("RAG pipeline initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize RAG pipeline: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down FastAPI application...")

# Create FastAPI application
app = FastAPI(
    title="TeleConnect RAG API",
    description="FastAPI server for Twilio integration with RAG pipeline",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Templates for TwiML responses
templates = Jinja2Templates(directory="templates")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "TeleConnect RAG API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    global rag_pipeline
    
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "rag_pipeline": rag_pipeline is not None,
            "api_keys": {
                "google_api_key": bool(os.getenv("GOOGLE_API_KEY")),
                "twilio_account_sid": bool(os.getenv("TWILIO_ACCOUNT_SID")),
                "twilio_auth_token": bool(os.getenv("TWILIO_AUTH_TOKEN"))
            }
        }
    }
    
    if rag_pipeline:
        try:
            stats = rag_pipeline.get_system_stats()
            health_status["rag_stats"] = stats
        except Exception as e:
            logger.error(f"Error getting RAG stats: {e}")
            health_status["services"]["rag_pipeline"] = False
    
    return health_status

@app.post("/api/chat")
async def chat_endpoint(request: Request):
    """Standard chat endpoint for text-based interactions"""
    global rag_pipeline
    
    if not rag_pipeline:
        raise HTTPException(status_code=503, detail="RAG pipeline not initialized")
    
    try:
        data = await request.json()
        query = data.get("query", "").strip()
        session_id = data.get("session_id")
        customer_info = data.get("customer_info", {})
        
        if not query:
            raise HTTPException(status_code=400, detail="Query is required")
        
        # Process query through RAG pipeline
        result = rag_pipeline.process_query(
            query=query,
            session_id=session_id,
            customer_info=customer_info
        )
        
        return {
            "response": result.get("response", ""),
            "intent": result.get("intent", {}),
            "context_sources": result.get("context_sources", []),
            "followup_questions": result.get("followup_questions", []),
            "session_id": result.get("session_id"),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chat/stream")
async def chat_stream_endpoint(request: Request):
    """Streaming chat endpoint"""
    global rag_pipeline
    
    if not rag_pipeline:
        raise HTTPException(status_code=503, detail="RAG pipeline not initialized")
    
    try:
        data = await request.json()
        query = data.get("query", "").strip()
        session_id = data.get("session_id")
        customer_info = data.get("customer_info", {})
        
        if not query:
            raise HTTPException(status_code=400, detail="Query is required")
        
        async def generate_response():
            """Generate streaming response"""
            try:
                # Process query through RAG pipeline
                result = rag_pipeline.process_query(
                    query=query,
                    session_id=session_id,
                    customer_info=customer_info
                )
                
                response_text = result.get("response", "")
                
                # Stream response word by word
                words = response_text.split()
                for i, word in enumerate(words):
                    chunk = {
                        "type": "text",
                        "content": word + " ",
                        "is_final": i == len(words) - 1
                    }
                    yield f"data: {chunk}\n\n"
                
                # Send final metadata
                final_chunk = {
                    "type": "metadata",
                    "intent": result.get("intent", {}),
                    "context_sources": result.get("context_sources", []),
                    "followup_questions": result.get("followup_questions", []),
                    "session_id": result.get("session_id"),
                    "is_final": True
                }
                yield f"data: {final_chunk}\n\n"
                
            except Exception as e:
                error_chunk = {
                    "type": "error",
                    "content": str(e),
                    "is_final": True
                }
                yield f"data: {error_chunk}\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in streaming chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "fastapi_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
