# Google Generative AI for Gemini (both embeddings and LLM)
google-generativeai

# ChromaDB for vector database
chromadb
# LangChain components for RAG framework
langchain
langchain-core
langchain-google-genai
langchain-chroma
langchain-text-splitters

# Streamlit for web interface
streamlit

# Environment management
python-dotenv

# Data processing and utilities
numpy
pandas

# HTTP requests and API handling
requests
httpx

# JSON and data serialization
orjson

# Logging and monitoring
rich

# Type hints and validation
pydantic
typing-extensions

# Text processing
tiktoken
tokenizers

# Database and storage
sqlalchemy

# Async support
anyio

# Security and authentication
bcrypt

# Configuration and settings
pyyaml
toml

# Development and testing (optional)
pytest
pytest-asyncio

# Additional utilities
tenacity
overrides
mmh3

# Voice input dependencies
SpeechRecognition
pyaudio
pydub

# Voice output dependencies
pyttsx3
gTTS

# FastAPI and Twilio integration dependencies
fastapi
uvicorn[standard]
twilio
python-multipart
jinja2
